# Responsible for logging events such as timeouts, kicks, bans, and leaves

import discord
from discord.ext import commands
from datetime import datetime
import typing
import asyncio

class Logging(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.STAFF_CHANNEL_ID = 1341206425002709045
        self.EMBED_COLOR = 0x566374
        
        # Custom emoji IDs for log events
        self.TIMEOUT_EMOJI = "<:Timer:1378881220175466626>"
        self.KICK_EMOJI = "<:Banned:1378881203134005258>"
        self.BAN_EMOJI = "<:Banned:1378881203134005258>"
        self.LEAVE_EMOJI = "<:Square_Leave:1378881218451603596>"

    def has_members_role(self, member: discord.Member) -> bool:
        """Check if the user has the Members role"""
        return discord.utils.get(member.roles, id=1339003367518765066) is not None

    @commands.Cog.listener()
    async def on_member_update(self, before: discord.Member, after: discord.Member):
        """Detect timeout status changes and log them"""
        # Check if user has Members role
        if not self.has_members_role(before):
            return

        # Check if timeout status changed
        if before.timed_out_until != after.timed_out_until:
            staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
            if not staff_channel:
                return

            # If timeout was removed (after.timed_out_until is None)
            if after.timed_out_until is None:
                try:
                    # Add a small delay to ensure the audit log is available
                    await asyncio.sleep(1)
                    
                    moderator = None
                    async for entry in after.guild.audit_logs(limit=10, action=discord.AuditLogAction.member_update):
                        if entry.target.id == after.id:
                            # Check if this entry is about a timeout
                            if hasattr(entry.changes.before, 'timed_out_until'):
                                moderator = entry.user
                                break

                    log_embed = discord.Embed(
                        title=f"<:Check:1379204527273807882>︲Timeout Removed︲{after.display_name}",
                        description=f"{after.mention}",
                        color=self.EMBED_COLOR
                    )
                    
                    log_embed.add_field(
                        name="Removed By:",
                        value=moderator.mention if moderator else "Unknown",
                        inline=False
                    )
                    
                    log_embed.set_thumbnail(url=after.display_avatar.url)
                    await staff_channel.send(embed=log_embed)
                    
                except Exception as e:
                    print(f"Error logging timeout removal: {e}")
                return

            # Fetch audit logs to get timeout reason and moderator
            moderator = None
            reason = "No reason provided"

            try:
                # Add a small delay to ensure the audit log is available
                await asyncio.sleep(1)
                
                async for entry in after.guild.audit_logs(limit=10, action=discord.AuditLogAction.member_update):
                    if entry.target.id == after.id:
                        # Check if this entry is about a timeout
                        if hasattr(entry.changes.before, 'timed_out_until'):
                            reason = entry.reason or "No reason provided"
                            moderator = entry.user
                            break
                        
            except Exception as e:
                print(f"Error fetching audit logs: {e}")
                print(f"Entry changes: {entry.changes if 'entry' in locals() else 'No entry found'}")
                moderator = None

            # Create embed for timeout log
            embed = discord.Embed(
                title=f"{self.TIMEOUT_EMOJI}︲Timed Out︲{after.display_name}",
                description=f"{after.mention}",
                color=self.EMBED_COLOR
            )

            # Add fields for each section
            embed.add_field(
                name="Reason:",
                value=reason,
                inline=False
            )

            embed.add_field(
                name="Expires:",
                value=f"<t:{int(after.timed_out_until.timestamp())}:f>",
                inline=False
            )

            embed.add_field(
                name="Timed Out By:",
                value=moderator.mention if moderator else 'Unknown',
                inline=False
            )

            # Set member avatar as thumbnail
            embed.set_thumbnail(url=after.display_avatar.url)

            # Send the embed to staff channel
            try:
                await staff_channel.send(embed=embed)
            except Exception as e:
                print(f"Error sending timeout log: {e}")

    @commands.Cog.listener()
    async def on_member_remove(self, member: discord.Member):
        """Detect members leaving the server"""
        if not self.has_members_role(member):
            return

        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if not staff_channel:
            return

        try:
            # Add small delay to ensure audit log is available
            await asyncio.sleep(1)
            
            # Check for kick or ban in audit logs
            was_kicked_or_banned = False
            
            # Check for kick
            async for entry in member.guild.audit_logs(limit=1, action=discord.AuditLogAction.kick):
                if entry.target.id == member.id and (discord.utils.utcnow() - entry.created_at).total_seconds() < 5:
                    was_kicked_or_banned = True
                    # It's a kick, create kick embed
                    embed = discord.Embed(
                        title=f"{self.KICK_EMOJI}︲Kicked | {member.display_name}",
                        description=f"{member.mention}",
                        color=self.EMBED_COLOR
                    )

                    embed.add_field(
                        name="Reason:",
                        value=entry.reason or "No reason provided",
                        inline=False
                    )

                    embed.add_field(
                        name="Kicked By:",
                        value=entry.user.mention,
                        inline=False
                    )

                    embed.set_thumbnail(url=member.display_avatar.url)
                    await staff_channel.send(embed=embed)
                    break

            # Check for ban if not kicked
            if not was_kicked_or_banned:
                async for entry in member.guild.audit_logs(limit=1, action=discord.AuditLogAction.ban):
                    if entry.target.id == member.id and (discord.utils.utcnow() - entry.created_at).total_seconds() < 5:
                        was_kicked_or_banned = True
                        break  # No need to create embed here as on_member_ban will handle it

            # If we get here and it wasn't a kick or ban, it's a regular leave
            if not was_kicked_or_banned:
                # Calculate time spent in server
                joined_at = member.joined_at
                left_at = discord.utils.utcnow()
                time_in_server = left_at - joined_at
                
                # If less than 24 hours
                if time_in_server.days == 0:
                    time_str = "Less Than 24 Hours"
                else:
                    years = time_in_server.days // 365
                    remaining_days = time_in_server.days % 365
                    months = remaining_days // 30
                    days = remaining_days % 30
                    
                    time_str = f"Years: {years}︱Months: {months}︱Days: {days}"

                embed = discord.Embed(
                    title=f"{self.LEAVE_EMOJI}︲Member Left︲{member.display_name}",
                    description=f"[User's Profile](https://discord.com/users/{member.id})",
                    color=self.EMBED_COLOR
                )

                embed.add_field(
                    name="Time Spent in Server:",
                    value=time_str,
                    inline=False
                )

                embed.set_thumbnail(url=member.display_avatar.url)
                await staff_channel.send(embed=embed)

        except Exception as e:
            print(f"Error in member remove event: {e}")

    @commands.Cog.listener()
    async def on_member_ban(self, guild: discord.Guild, user: discord.User):
        """Detect bans and send log"""
        # For bans, we need to check the user's roles before they were banned
        member = guild.get_member(user.id)
        if member and not self.has_members_role(member):
            return

        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if not staff_channel:
            return

        try:
            await asyncio.sleep(1)
            async for entry in guild.audit_logs(limit=1, action=discord.AuditLogAction.ban):
                if entry.target.id == user.id:
                    embed = discord.Embed(
                        title=f"{self.BAN_EMOJI}︲Banned︲{user.display_name}",
                        description=f"{user.mention}",
                        color=self.EMBED_COLOR
                    )

                    embed.add_field(
                        name="Reason:",
                        value=entry.reason or "No reason provided",
                        inline=False
                    )

                    embed.add_field(
                        name="Banned By:",
                        value=entry.user.mention,
                        inline=False
                    )

                    embed.set_thumbnail(url=user.display_avatar.url)

                    await staff_channel.send(embed=embed)
                    return

        except Exception as e:
            print(f"Error logging ban: {e}")

    @commands.Cog.listener()
    async def on_member_timeout_remove(self, member: discord.Member):
        """Log when a member's timeout is removed"""
        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if staff_channel:
            try:
                # Get audit log for the timeout removal
                async for entry in member.guild.audit_logs(limit=1, action=discord.AuditLogAction.member_update):
                    if entry.target.id == member.id:
                        moderator = entry.user
                        break
                else:
                    moderator = None

                log_embed = discord.Embed(
                    title=f"<:Check:1379204527273807882>︲Timeout Removed︲{member.display_name}",
                    description=f"{member.mention}",
                    color=self.EMBED_COLOR
                )
                
                log_embed.add_field(
                    name="Removed By:",
                    value=moderator.mention if moderator else "Unknown",
                    inline=False
                )
                
                log_embed.set_thumbnail(url=member.display_avatar.url)
                await staff_channel.send(embed=log_embed)
            except Exception as e:
                print(f"Error logging timeout removal: {e}")

    @commands.Cog.listener()
    async def on_member_unban(self, guild: discord.Guild, user: discord.User):
        """Log when a member is unbanned"""
        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if staff_channel:
            try:
                # Get audit log for the unban
                async for entry in guild.audit_logs(limit=1, action=discord.AuditLogAction.unban):
                    if entry.target.id == user.id:
                        moderator = entry.user
                        break
                else:
                    moderator = None

                log_embed = discord.Embed(
                    title=f"<:Check:1379204527273807882>︲Ban Removed︲{user.display_name}",
                    description=f"{user.mention}",
                    color=self.EMBED_COLOR
                )
                
                log_embed.add_field(
                    name="Removed By:",
                    value=moderator.mention if moderator else "Unknown",
                    inline=False
                )
                
                log_embed.set_thumbnail(url=user.display_avatar.url)
                await staff_channel.send(embed=log_embed)
            except Exception as e:
                print(f"Error logging unban: {e}")

async def setup(bot):
    await bot.add_cog(Logging(bot))

