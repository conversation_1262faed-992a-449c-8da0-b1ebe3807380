import os
import asyncio
from discord.ext import commands
import discord
from discord import app_commands
from dotenv import load_dotenv
import sys
import json
import subprocess
from cogs.db import db


# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')

# Set up data directory for Railway's persistent storage
# Railway provides persistent storage at /data, fallback to local 'data' for development
DATA_DIR = os.getenv('RAILWAY_DATA_DIR', 'data')
try:
    os.makedirs(DATA_DIR, mode=0o777, exist_ok=True)
    print(f"✓ Data directory set to: {DATA_DIR}")
except Exception as e:
    print(f"Failed to create data directory: {e}")

# Make DATA_DIR available globally for cogs to use
os.environ['BOT_DATA_DIR'] = DATA_DIR

async def update_railway_admin_roles():
    """Automatically update Railway's ADMIN_ROLES to match local .env file"""
    try:
        # Only run this on Railway (check for Railway environment)
        if not os.getenv('RAILWAY_ENVIRONMENT'):
            print("ℹ️ Not running on Railway, skipping ADMIN_ROLES sync")
            return

        # Get current ADMIN_ROLES from .env
        current_admin_roles = os.getenv('ADMIN_ROLES')
        if not current_admin_roles:
            print("⚠️ ADMIN_ROLES not found in environment variables")
            return

        print(f"🔄 Syncing Railway ADMIN_ROLES with local environment...")
        print(f"📋 Local ADMIN_ROLES: {current_admin_roles}")

        # Check if railway CLI is available
        try:
            result = subprocess.run(['railway', '--version'],
                                  capture_output=True, text=True, check=True, timeout=10)
            print(f"✓ Railway CLI available: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            print("⚠️ Railway CLI not available, skipping ADMIN_ROLES sync")
            return

        # Get current Railway ADMIN_ROLES
        try:
            result = subprocess.run(['railway', 'variables'],
                                  capture_output=True, text=True, check=True, timeout=15)

            railway_admin_roles = None
            for line in result.stdout.split('\n'):
                if 'ADMIN_ROLES' in line and '=' in line:
                    railway_admin_roles = line.split('=', 1)[1].strip()
                    break

            if railway_admin_roles == current_admin_roles:
                print("✅ Railway ADMIN_ROLES already matches local environment")
                return

            print(f"📋 Railway ADMIN_ROLES: {railway_admin_roles or 'Not set'}")

        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            print(f"⚠️ Could not check Railway variables: {e}")
            # Continue with update anyway

        # Update Railway ADMIN_ROLES
        try:
            cmd = ['railway', 'variables', 'set', f'ADMIN_ROLES={current_admin_roles}']
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, timeout=20)
            print(f"✅ Successfully updated Railway ADMIN_ROLES")
            print("ℹ️ Note: Changes will take effect on next deployment")

        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            print(f"❌ Failed to update Railway ADMIN_ROLES: {e}")
            if hasattr(e, 'stderr') and e.stderr:
                print(f"Error details: {e.stderr}")

    except Exception as e:
        print(f"❌ Error during Railway ADMIN_ROLES sync: {e}")
        # Don't let this stop the bot from starting

class Bot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.members = True
        
        super().__init__(
            command_prefix='!',
            intents=intents
        )
        
        # Track if we've synced commands
        self._sync_completed = False

    async def setup_hook(self):
        """Sets up the bot's extensions and syncs commands"""

        try:
            # Auto-update Railway ADMIN_ROLES on startup
            await update_railway_admin_roles()

            # Initialize database connection
            print("Connecting to database...")
            await db.connect()
            print("✓ Database connected successfully")
            
            print("Loading cogs...")
            # Get the absolute path to the cogs directory
            script_dir = os.path.dirname(os.path.abspath(__file__))
            cogs_dir = os.path.join(script_dir, 'cogs')
            
            # Keep track of loaded cogs to prevent duplicates
            loaded_cogs = set()
            
            # Files to skip when loading cogs (utility modules, not actual cogs)
            skip_files = {'db.py', '__init__.py'}
            
            for filename in os.listdir(cogs_dir):
                if filename.endswith('.py') and filename not in skip_files:
                    cog_name = f'cogs.{filename[:-3]}'  # Include 'cogs.' prefix
                    if cog_name not in loaded_cogs:  # Only load if not already loaded
                        try:
                            await self.load_extension(cog_name)
                            loaded_cogs.add(cog_name)
                            print(f"✓ Loaded {filename}")
                        except Exception as e:
                            print(f"❌ Failed to load {filename}: {e}")
            
            # Wait a short moment to ensure all cogs are fully loaded
            await asyncio.sleep(1)
            
            print("Syncing commands...")
            try:
                # Check if we have any commands to sync
                commands = self.tree.get_commands()
                print(f"Found {len(commands)} commands to sync")
                for cmd in commands:
                    print(f"  - {cmd.name}: {cmd.description}")
                
                if len(commands) == 0:
                    print("⚠️ No commands found to sync!")
                else:
                    # Sync globally first (this is usually sufficient)
                    synced = await self.tree.sync()
                    print(f"✓ Synced {len(synced)} commands globally")
                    
                    # Also sync for each guild if needed
                    for guild in self.guilds:
                        try:
                            self.tree.copy_global_to(guild=guild)
                            guild_synced = await self.tree.sync(guild=guild)
                            print(f"✓ Synced {len(guild_synced)} commands for guild: {guild.name}")
                        except Exception as guild_error:
                            print(f"❌ Failed to sync commands for guild {guild.name}: {guild_error}")
                
                self._sync_completed = True
            except Exception as e:
                print(f"❌ Error during command sync: {e}")
                import traceback
                traceback.print_exc()
            
        except Exception as e:
            print(f"Error during setup: {e}")

    async def on_ready(self):
        print(f"✓ Logged in as {self.user} (ID: {self.user.id})")
        print(f"✓ Bot is in {len(self.guilds)} guild(s)")
        for guild in self.guilds:
            print(f"  - {guild.name} (ID: {guild.id})")
        
        # Only attempt to sync if we haven't already
        if not self._sync_completed:
            try:
                print("Attempting command sync from on_ready...")
                commands = self.tree.get_commands()
                print(f"Found {len(commands)} commands to sync")
                
                synced = await self.tree.sync()
                self._sync_completed = True
                print(f"✓ Synced {len(synced)} commands during ready event")
            except Exception as e:
                print(f"❌ Error syncing commands during ready: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("Commands already synced, skipping sync in on_ready")

async def main():
    if not TOKEN:
        raise ValueError("No Discord token found. Make sure your .env file contains: DISCORD_TOKEN=your_token_here")
    
    try:
        async with Bot() as bot:
            await bot.start(TOKEN)
    except KeyboardInterrupt:
        sys.exit(0)
    except Exception as e:
        print(f"ERROR: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        sys.exit(0)
    except Exception as e:
        print(f"ERROR: {e}")
        sys.exit(1)







