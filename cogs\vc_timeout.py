# Responsible for handling the voice call timeout command

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
from datetime import datetime, timedelta
import json
import os
from dotenv import load_dotenv

class VCTimeout(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.STAFF_CHANNEL_ID = 1341206425002709045
        self.EMBED_COLOR = 0x566374
        self.VC_TIMEOUT_EMOJI = "<:Timer:1348851423819075694>"
        
        # In-memory storage for VC timeouts (user_id -> expiry_timestamp)
        self.vc_timeouts = {}
        # Store original permissions to restore later
        self.original_permissions = {}
        
        load_dotenv()
        default_admin_roles = ['1378193717160312862', '1379995762016125048', '1338996580253958164']
        self.admin_roles = os.getenv('ADMIN_ROLES', ','.join(default_admin_roles)).split(',')

    def has_admin_role(self, member: discord.Member) -> bool:
        return any(str(role.id) in self.admin_roles for role in member.roles)

    def is_user_timed_out(self, user_id: int) -> bool:
        """Check if user is currently timed out from voice channels"""
        if user_id not in self.vc_timeouts:
            return False
        
        current_time = datetime.utcnow().timestamp()
        timeout_end = self.vc_timeouts[user_id]
        
        # If timeout has expired, remove it
        if current_time > timeout_end:
            del self.vc_timeouts[user_id]
            print(f"✅ Removed expired VC timeout for user {user_id}")
            return False
        
        return True

    def add_vc_timeout(self, user_id: int, timeout_timestamp: float):
        """Add a VC timeout for a user"""
        self.vc_timeouts[user_id] = timeout_timestamp
        print(f"✅ Added VC timeout for user {user_id} until {datetime.fromtimestamp(timeout_timestamp)}")

    def remove_vc_timeout(self, user_id: int):
        """Remove a VC timeout for a user"""
        if user_id in self.vc_timeouts:
            del self.vc_timeouts[user_id]
            print(f"✅ Removed VC timeout for user {user_id}")

    def get_timeout_end(self, user_id: int) -> float:
        """Get the timeout end timestamp for a user"""
        return self.vc_timeouts.get(user_id, 0)

    async def apply_voice_channel_restrictions(self, member: discord.Member):
        """Remove connect permissions from all voice channels for a member"""
        try:
            guild = member.guild
            self.original_permissions[member.id] = {}
            
            # Get all voice channels in the guild
            voice_channels = [channel for channel in guild.channels if isinstance(channel, discord.VoiceChannel)]
            
            for channel in voice_channels:
                # Store original permissions
                original_perms = channel.overwrites_for(member)
                self.original_permissions[member.id][channel.id] = original_perms
                
                # Create new permissions denying connect
                new_perms = original_perms
                new_perms.connect = False
                
                # Apply the restriction
                await channel.set_permissions(member, overwrite=new_perms, reason="Voice call timeout restriction")
            
            print(f"🚫 Applied voice channel restrictions to {member.display_name} on {len(voice_channels)} channels")
            
        except Exception as e:
            print(f"❌ Error applying voice restrictions to {member.display_name}: {e}")

    async def remove_voice_channel_restrictions(self, member: discord.Member):
        """Completely remove all permission overrides for a member from all voice channels"""
        try:
            guild = member.guild
            
            # Get all voice channels in the guild
            voice_channels = [channel for channel in guild.channels if isinstance(channel, discord.VoiceChannel)]
            
            for channel in voice_channels:
                # Force remove any permission overrides for this member
                try:
                    await channel.set_permissions(member, overwrite=None, reason="Voice call timeout restriction removed - clearing all overrides")
                    print(f"🗑️ Cleared permission overrides for {member.display_name} in {channel.name}")
                except Exception as channel_error:
                    print(f"❌ Error clearing permissions in {channel.name} for {member.display_name}: {channel_error}")
            
            print(f"✅ Completely removed all voice channel restrictions from {member.display_name}")
            
        except Exception as e:
            print(f"❌ Error removing voice restrictions from {member.display_name}: {e}")

    @app_commands.command(name="voicecalltimeout", description="Restrict a user from joining voice calls for a set duration")
    @app_commands.choices(duration=[
        app_commands.Choice(name="5 Minutes", value=5),
        app_commands.Choice(name="10 Minutes", value=10),
        app_commands.Choice(name="1 Hour", value=60),
        app_commands.Choice(name="1 Day", value=1440),
        app_commands.Choice(name="1 Week", value=10080)
    ])
    @app_commands.default_permissions(moderate_members=True)
    @app_commands.describe(
        member="The member to timeout",
        duration="Duration of the timeout",
        reason="Reason for the timeout"
    )
    async def vc_timeout(self, interaction: discord.Interaction, member: discord.Member, duration: app_commands.Choice[int], reason: str = "No reason provided"):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return
        
        try:
            # Defer the response immediately
            await interaction.response.defer(ephemeral=True)

            # Check if user has permission
            if not interaction.user.guild_permissions.moderate_members:
                await interaction.followup.send("You don't have permission to use this command.", ephemeral=True)
                return

            # Check if trying to timeout someone with higher role
            if member.top_role >= interaction.user.top_role and interaction.user.id != interaction.guild.owner_id:
                await interaction.followup.send("You cannot timeout members with higher or equal roles.", ephemeral=True)
                return

            # Calculate end time
            end_time = datetime.utcnow() + timedelta(minutes=duration.value)
            
            # Store timeout in memory
            self.add_vc_timeout(member.id, end_time.timestamp())

            # Apply voice channel restrictions
            await self.apply_voice_channel_restrictions(member)

            # If user is in VC, disconnect them
            if member.voice and member.voice.channel:
                await member.move_to(None)
                print(f"🚫 Disconnected {member.display_name} from voice channel due to timeout")

            # Create and send log embed
            staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
            if staff_channel:
                embed = discord.Embed(
                    title=f"{self.VC_TIMEOUT_EMOJI}︲Voice Call Timeout︲{member.name}",
                    description=f"[User's Profile](https://discord.com/users/{member.id})",
                    color=self.EMBED_COLOR
                )
                embed.add_field(name="Reason:", value=reason, inline=False)
                embed.add_field(name="Expires:", value=f"<t:{int(end_time.timestamp())}:f>", inline=False)
                embed.add_field(name="Timed Out By:", value=interaction.user.mention, inline=False)
                embed.set_thumbnail(url=member.display_avatar.url)
                
                await staff_channel.send(embed=embed)

            # Notify the user via DM
            try:
                user_embed = discord.Embed(
                    title="Voice Call Timeout",
                    description=f"You have been timed out from voice calls in {interaction.guild.name}",
                    color=self.EMBED_COLOR
                )
                user_embed.add_field(name="Reason:", value=reason, inline=False)
                user_embed.add_field(name="Expires:", value=f"<t:{int(end_time.timestamp())}:f>", inline=False)
                await member.send(embed=user_embed)
            except discord.Forbidden:
                pass  # User has DMs disabled

            await interaction.followup.send(f"Voice call timeout applied to {member.mention} for [ {duration.name} ]", ephemeral=True)

        except Exception as e:
            print(f"❌ Error in vc_timeout command: {e}")
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="voicecalluntimeout", description="Lift a user's voice call timeout restriction")
    @app_commands.default_permissions(moderate_members=True)
    async def vc_untimeout(self, interaction: discord.Interaction, member: discord.Member):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return
        
        try:
            # Defer the response immediately
            await interaction.response.defer(ephemeral=True)

            if not interaction.user.guild_permissions.moderate_members:
                await interaction.followup.send("You don't have permission to use this command.", ephemeral=True)
                return

            # Check if user is currently timed out
            if member.id in self.vc_timeouts:
                self.remove_vc_timeout(member.id)
                
                # Remove voice channel restrictions
                await self.remove_voice_channel_restrictions(member)
                
                # Also clean up stored permissions
                if member.id in self.original_permissions:
                    del self.original_permissions[member.id]
                    print(f"🧹 Cleaned up stored permissions for {member.display_name}")

                # Notify the user
                try:
                    await member.send(f"Your voice call timeout in {interaction.guild.name} has been removed.")
                except discord.Forbidden:
                    pass

                await interaction.followup.send(f"Voice call timeout removed from {member.mention}", ephemeral=True)
            else:
                await interaction.followup.send(f"{member.mention} is not voice call timed out", ephemeral=True)

        except Exception as e:
            print(f"❌ Error in vc_untimeout command: {e}")
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="clearvcpermissions", description="[DEBUG] Manually clear all voice channel permissions for a user")
    @app_commands.default_permissions(moderate_members=True)
    async def clear_vc_permissions(self, interaction: discord.Interaction, member: discord.Member):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return
        
        try:
            await interaction.response.defer(ephemeral=True)
            
            # Force clear all voice channel permissions
            await self.remove_voice_channel_restrictions(member)
            
            # Also remove from timeout list if they're there
            if member.id in self.vc_timeouts:
                self.remove_vc_timeout(member.id)
            
            # Clean up stored permissions
            if member.id in self.original_permissions:
                del self.original_permissions[member.id]
                print(f"🧹 Cleaned up stored permissions for {member.display_name}")
            
            await interaction.followup.send(f"✅ Cleared all voice channel permissions for {member.mention}", ephemeral=True)
            
        except Exception as e:
            print(f"❌ Error in clear_vc_permissions command: {e}")
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

    @commands.Cog.listener()
    async def on_voice_state_update(self, member: discord.Member, before: discord.VoiceState, after: discord.VoiceState):
        # This listener is now mainly for logging - permissions prevent joining
        # But we can still check for expired timeouts when users try to join
        if after.channel is not None and member.id in self.vc_timeouts:
            # Check if timeout has expired and clean up if needed
            if self.is_user_timed_out(member.id):
                print(f"🚫 User {member.display_name} tried to join voice channel but is still timed out")
            else:
                print(f"✅ User {member.display_name} timeout expired, they can now join voice channels")

    @commands.Cog.listener()
    async def on_ready(self):
        print("✅ VCTimeout cog ready")
        # Start cleanup task
        self.bot.loop.create_task(self.cleanup_expired_timeouts())
    
    async def cleanup_expired_timeouts(self):
        """Periodically clean up expired timeouts"""
        while not self.bot.is_closed():
            try:
                current_time = datetime.utcnow().timestamp()
                
                # Find expired timeouts
                expired_users = []
                for user_id, timeout_end in self.vc_timeouts.items():
                    if current_time > timeout_end:
                        expired_users.append(user_id)
                
                # Remove expired timeouts and their permissions
                for user_id in expired_users:
                    # Remove from timeout list
                    del self.vc_timeouts[user_id]
                    
                    # Try to remove voice channel restrictions
                    try:
                        guild = self.bot.guilds[0] if self.bot.guilds else None  # Assuming single guild bot
                        if guild:
                            member = guild.get_member(user_id)
                            if member:
                                await self.remove_voice_channel_restrictions(member)
                    except Exception as e:
                        print(f"❌ Error removing permissions for expired timeout user {user_id}: {e}")
                    
                    print(f"🧹 Cleaned up expired VC timeout for user {user_id}")
                
                # Wait 5 minutes before next cleanup
                await asyncio.sleep(300)
                
            except Exception as e:
                print(f"❌ Error in cleanup task: {e}")
                await asyncio.sleep(300)  # Wait before retrying

async def setup(bot):
    await bot.add_cog(VCTimeout(bot))



